<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Gallery - Extracted from curl-commands.log</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            color: white;
        }

        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .image-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }

        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .image-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 10px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .image-container:hover img {
            transform: scale(1.05);
        }

        .image-info {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .image-url {
            font-size: 0.8rem;
            color: #666;
            word-break: break-all;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .image-filename {
            font-weight: bold;
            color: #333;
            font-size: 0.9rem;
        }

        .loading {
            display: none;
            color: #666;
            font-size: 0.9rem;
            text-align: center;
            padding: 20px;
        }

        .error {
            color: #e74c3c;
            font-size: 0.8rem;
            text-align: center;
            padding: 10px;
            background: #ffeaea;
            border-radius: 5px;
            margin-top: 10px;
        }

        .fullscreen-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            color: white;
            font-size: 40px;
            cursor: pointer;
            z-index: 1001;
        }

        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .gallery {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ Image Gallery</h1>
            <p>Images extracted from curl-commands.log</p>
        </div>

        <div class="stats">
            <h3 id="imageCount">Loading images...</h3>
        </div>

        <div class="gallery" id="gallery">
            <!-- Images will be loaded here -->
        </div>

        <div class="footer">
            <p>Generated from curl-commands.log • Click any image to view in fullscreen</p>
        </div>
    </div>

    <div class="fullscreen-overlay" id="fullscreenOverlay">
        <span class="close-btn" onclick="closeFullscreen()">&times;</span>
        <img class="fullscreen-image" id="fullscreenImage" alt="Fullscreen view">
    </div>

    <script>
        const imageUrls = [
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66b5b7488028e8.92582684/c36q4i0mpkr.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66b5b7488028e8.92582684/zvwcvqxdse.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/4j34lktay98.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/6cf4qmovier.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/8kg2fq6gqv6.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/bb22msa1rwo.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/brt6xffnauk.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/klerle2axw.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/ov15o5f3an.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/qeqb1jwad7.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/r3ubpexryo.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/u4yux3mt68m.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir66d03ce1483de8.65065869/utbk77gejn.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir67444c90e91120.96044956/24xvw9dtn6dj.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir67444c90e91120.96044956/5va476m3opj.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir67444c90e91120.96044956/lu59ek87qg9.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir67444c90e91120.96044956/otrohckn3cr.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir67444c90e91120.96044956/ta9i4ulvcwc.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6775ec7b91a7a0.47609296/1nf6xqt5xny.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6775ec7b91a7a0.47609296/5mf5r0n7sa.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6775ec7b91a7a0.47609296/kym7o6l35d.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6775ec7b91a7a0.47609296/rc3l0cy6o3.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6777bb2d221d80.40338042/27gq89noos2.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6777bb2d221d80.40338042/5bi1zesrhcd.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir6777bb2d221d80.40338042/eih5rop3fkh.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/0n8yaa1y146l.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/3an99uepbz6.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/3qjzplfvi18.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/47wa78t7s1g.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/4qp0m48py8i.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/5d7el55n0yf.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/5o3lcg0tim.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/68u7ylsd8im.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/8vsarmsp7z.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/8x3689x5c87.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/9f2knzb1g0s.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/9gpp5bqt2ev.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/9pr82kphp09.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/ayov95na60u.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/b8f098re47q.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/c1w1e3fi4k4.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/eq4vmuhzx2q.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/fw670pjkdmd.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/g0jtmdw7xt9.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/juqf84i0cjr.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/k0swqzedl3n.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/k6q3ahqpn5c.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/kw3u6wm978s.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/kweucxk9i2h.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/m9lfum54vw.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/mpi8s41oyfl.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/mwbg31wtxq.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/mz9q3l3hml.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/nqag3l1tazd.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/o3zx6gvg73.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/pyst0ef9dmd.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/qvh1tnx9aab.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/r3u2xweqhc.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/r8jv3dupep.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/rbbnl62z29n.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/t4zpki38tlf.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/trtiqo4c27.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/tvc7zfxjx8.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/u2v7xfl130k.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/w3a2r91yrg.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/wfmrxyqmmut.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/wvwqq5436i.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/xb979e2cwil.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/xkwz532o21.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/xymbre04u2o.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/y0cqyi8vi2.jpg",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/z3cz3setdg8.png",
            "https://static-shop.aixingheshangcheng.com/data/upload/test/dir677c8a73e12c19.86357826/zrs1i3gn78j.png"
        ];

        let loadedImages = 0;
        let failedImages = 0;

        function getFileName(url) {
            return url.split('/').pop().split('?')[0];
        }

        function createImageCard(url, index) {
            const card = document.createElement('div');
            card.className = 'image-card';
            
            const imageContainer = document.createElement('div');
            imageContainer.className = 'image-container';
            
            const img = document.createElement('img');
            img.src = url;
            img.alt = getFileName(url);
            img.loading = 'lazy';
            
            const loading = document.createElement('div');
            loading.className = 'loading';
            loading.textContent = 'Loading...';
            loading.style.display = 'block';
            
            const imageInfo = document.createElement('div');
            imageInfo.className = 'image-info';
            
            const filename = document.createElement('div');
            filename.className = 'image-filename';
            filename.textContent = getFileName(url);
            
            const urlDiv = document.createElement('div');
            urlDiv.className = 'image-url';
            urlDiv.textContent = url;
            
            img.onload = function() {
                loading.style.display = 'none';
                loadedImages++;
                updateStats();
                
                // Add click event for fullscreen
                img.style.cursor = 'pointer';
                img.onclick = function() {
                    openFullscreen(url);
                };
            };
            
            img.onerror = function() {
                loading.style.display = 'none';
                const error = document.createElement('div');
                error.className = 'error';
                error.textContent = 'Failed to load image';
                imageContainer.appendChild(error);
                failedImages++;
                updateStats();
            };
            
            imageContainer.appendChild(loading);
            imageContainer.appendChild(img);
            imageInfo.appendChild(filename);
            imageInfo.appendChild(urlDiv);
            card.appendChild(imageContainer);
            card.appendChild(imageInfo);
            
            return card;
        }

        function updateStats() {
            const total = imageUrls.length;
            const pending = total - loadedImages - failedImages;
            document.getElementById('imageCount').innerHTML = 
                `📊 Total: ${total} images | ✅ Loaded: ${loadedImages} | ❌ Failed: ${failedImages} | ⏳ Loading: ${pending}`;
        }

        function openFullscreen(url) {
            const overlay = document.getElementById('fullscreenOverlay');
            const img = document.getElementById('fullscreenImage');
            img.src = url;
            overlay.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeFullscreen() {
            const overlay = document.getElementById('fullscreenOverlay');
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close fullscreen on ESC key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFullscreen();
            }
        });

        // Close fullscreen on overlay click
        document.getElementById('fullscreenOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFullscreen();
            }
        });

        // Initialize gallery
        function initGallery() {
            const gallery = document.getElementById('gallery');
            
            imageUrls.forEach((url, index) => {
                const card = createImageCard(url, index);
                gallery.appendChild(card);
            });
            
            updateStats();
        }

        // Start loading images when page loads
        document.addEventListener('DOMContentLoaded', initGallery);
    </script>
</body>
</html>
