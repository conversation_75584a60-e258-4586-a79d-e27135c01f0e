#!/usr/bin/env -S deno run

// Test the Unicode decoding function
function decodeUnicodeIn<PERSON>son(jsonString: string): string {
  try {
    // First, try to parse as JSON to validate it
    const parsed = JSON.parse(jsonString);
    
    // Convert back to JSON with proper Unicode decoding
    // JSON.stringify will properly handle Unicode characters
    return JSON.stringify(parsed, null, 2);
  } catch (error) {
    // If it's not valid JSON, just decode Unicode escape sequences manually
    return jsonString.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
      return String.fromCharCode(parseInt(hex, 16));
    });
  }
}

// Test cases
const testCases = [
  // Test with escaped JSON (like what you see in curl-commands.log)
  '{"title":"\\u6587\\u672c\\u4f4d\\u7f6e","name":"txtStyle"}',
  
  // Test with URL containing Unicode
  '{"url":"https:\\/\\/static-shop.aixingheshangcheng.com\\/data\\/upload\\/test\\/dir677c8a73e12c19.86357826\\/m9lfum54vw.png"}',
  
  // Test with mixed content
  '{"name":"\\u6837\\u5f0f\\u4e00","icon":"iconyangshi1","count":1}',
  
  // Test non-JSON string with Unicode
  'Hello \\u4e16\\u754c (World)',
];

console.log("🧪 Testing Unicode decoding function:\n");

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}:`);
  console.log(`Input:  ${testCase}`);
  console.log(`Output: ${decodeUnicodeInJson(testCase)}`);
  console.log("---");
});
