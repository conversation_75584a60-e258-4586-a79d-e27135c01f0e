
const url = "http://shop-api.aixingheshangcheng.com/api/v1/index/diy";

const headers = {
  "accept": "application/json",
  "accept-encoding": "gzip",
  "authorization": "Bearer 18387552|lcmBZC6piBAFk4RqSzxO5E8YN6rRku2daBI5y1Yq",
  "connection": "keep-alive",
  "content-type": "application/json",
  "cookie": "acw_tc=ac11000117514721990822713e005e7fbb50961d5bd8acb088794390bab560",
  "host": "shop-api.aixingheshangcheng.com",
  "platform": "android",
  "user-agent": "Mozilla/5.0 (Linux; Android 9; MI 6 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.115 Mobile Safari/537.36 uni-app Html5Plus/1.0 (Immersed/24.0)",
  "version": "3.9.3",
};

const body = JSON.stringify({
  "id": "0",
  "type": "1",
});

let lastResponseBody: string | null = null;

async function makeRequest() {
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: body,
    });

    if (!response.ok) {
      console.error(`Request failed with status: ${response.status}`);
      return;
    }

    const responseBody = await response.text();

    if (lastResponseBody === null) {
      console.log("Initial request successful. Monitoring for changes...");
      lastResponseBody = responseBody;
    } else if (lastResponseBody !== responseBody) {
      console.log("Response body has changed!");
      console.log("Previous response body:");
      console.log(lastResponseBody);
      console.log("New response body:");
      console.log(responseBody);
      lastResponseBody = responseBody;
    } else {
      console.log("No changes detected.");
    }
  } catch (error) {
    console.error("An error occurred:", error);
  }
}

// Run the request every 5 minutes (300,000 milliseconds)
setInterval(makeRequest, 5 * 60 * 1000);

// Initial request
makeRequest();
