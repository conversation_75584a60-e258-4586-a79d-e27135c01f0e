# Deno Proxy Server

A simple proxy server built with Deno (TypeScript) that forwards HTTP requests to `https://shop-api.aixingheshangcheng.com` while logging request details.

## Features

- ✅ Forwards all HTTP methods (GET, POST, PUT, DELETE, etc.)
- ✅ Preserves request headers and body
- ✅ Updates Host header to target domain
- ✅ Logs request path and POST data
- ✅ CORS support for browser requests
- ✅ Error handling and graceful shutdown

## Prerequisites

Make sure you have Deno installed. If not, install it from [deno.land](https://deno.land/):

```bash
curl -fsSL https://deno.land/x/install/install.sh | sh
```

## Usage

### Start the proxy server:

```bash
deno run --allow-net proxy-server.ts
```

The server will start on `http://localhost:8000` by default.

### Alternative with execution permissions:

```bash
chmod +x proxy-server.ts
./proxy-server.ts
```

## How it works

1. The proxy server listens on port 8000
2. Any request sent to `http://localhost:8000/path` gets forwarded to `https://shop-api.aixingheshangcheng.com/path`
3. The Host header is automatically updated to match the target domain
4. Request details are logged to the console:
   - HTTP method
   - Request path
   - Headers
   - Body (for POST requests)

## Example Usage

### GET Request
```bash
curl http://localhost:8000/api/products
```

### POST Request
```bash
curl -X POST http://localhost:8000/api/orders \
  -H "Content-Type: application/json" \
  -d '{"product_id": 123, "quantity": 2}'
```

### From Browser
You can also make requests from a web browser or JavaScript:

```javascript
fetch('http://localhost:8000/api/data', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ key: 'value' })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Configuration

You can modify the following constants in `proxy-server.ts`:

- `TARGET_HOST`: The target API URL (default: `https://shop-api.aixingheshangcheng.com`)
- `PORT`: The local port to listen on (default: `8000`)

## Stopping the Server

Press `Ctrl+C` to gracefully stop the server.
