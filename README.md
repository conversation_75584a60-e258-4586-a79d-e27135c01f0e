# API Monitor Proxy

A comprehensive Deno-based API monitoring and proxy system with diff detection and WeChat Work (企业微信) webhook notifications.

## 🚀 Features

### API Monitoring
- ✅ **Real-time API monitoring** with configurable intervals
- ✅ **Diff detection** - automatically detects changes in API responses
- ✅ **WeChat Work notifications** - sends alerts when changes are detected
- ✅ **Comprehensive logging** with timestamps and detailed information
- ✅ **Environment-based configuration** for easy deployment

### Proxy Server
- ✅ **HTTP proxy** - forwards all HTTP methods (GET, POST, PUT, DELETE, etc.)
- ✅ **Request logging** - logs all requests with headers and body
- ✅ **CORS support** for browser requests
- ✅ **Unicode decoding** for proper display of international content
- ✅ **Curl command generation** for easy debugging

### DevOps & Deployment
- ✅ **Docker support** with multi-service compose setup
- ✅ **Environment configuration** with .env file support
- ✅ **Log management** with optional web-based log viewer
- ✅ **Graceful shutdown** handling
- ✅ **Open source ready** with proper licensing and documentation

## 📋 Prerequisites

- **Deno** 1.40+ - Install from [deno.land](https://deno.land/)
- **Docker** (optional) - For containerized deployment
- **WeChat Work account** (optional) - For webhook notifications

```bash
# Install Deno
curl -fsSL https://deno.land/x/install/install.sh | sh
```

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <your-repo-url>
cd api-monitor-proxy

# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### 2. Configure Environment

Edit `.env` file with your settings:

```bash
# API Configuration
API_URL=https://your-api-endpoint.com/api/v1/endpoint
API_AUTHORIZATION=Bearer your-token-here
API_COOKIE=your-cookie-here

# WeChat Work Webhook (optional)
WECOM_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key

# Monitoring Configuration
MONITOR_INTERVAL_MINUTES=5
PROXY_PORT=8000
```

### 3. Run with Deno

```bash
# Start API monitor
deno task start:monitor

# Start proxy server
deno task start:proxy

# Development mode with auto-reload
deno task dev:monitor
deno task dev:proxy
```

### 4. Run with Docker

```bash
# Quick setup
./scripts/docker-setup.sh

# Start all services
docker-compose up -d

# Start specific services
docker-compose up monitor  # Monitor only
docker-compose up proxy    # Proxy only

# With log viewer
docker-compose --profile logs up -d
```

## 📖 Documentation

### Core Components

- **[Monitor](src/monitor.ts)** - API monitoring with diff detection
- **[Proxy Server](src/proxy-server.ts)** - HTTP proxy with logging
- **[WeChat Work Integration](src/wecom-webhook.ts)** - Webhook notifications
- **[Configuration](src/config.ts)** - Environment-based config management

### Setup Guides

- **[WeChat Work Setup](docs/WECOM_SETUP.md)** - Complete webhook configuration guide
- **[Docker Deployment](docker-compose.yml)** - Container orchestration setup

### API Monitoring

The monitor continuously checks your API endpoint and:

1. **Detects Changes** - Compares response content with previous requests
2. **Logs Activity** - Records all requests and changes with timestamps
3. **Sends Notifications** - Alerts via WeChat Work when changes occur
4. **Handles Errors** - Gracefully manages network issues and API failures

Example notification:
```
🔔 API 响应变化检测

时间: 2025-01-01 20:00:00
API: https://your-api.com/endpoint
变化: 内容长度 1234 → 1456 (+222 字符)

详细信息:
- 监控系统检测到 API 响应内容发生变化
- 建议检查相关业务逻辑是否受到影响
```

### Proxy Server

The proxy server provides:

- **Request Forwarding** - Transparent proxy to your target API
- **Request Logging** - Detailed logs of all HTTP traffic
- **CORS Support** - Browser-compatible cross-origin requests
- **Debug Tools** - Automatic curl command generation

Access the proxy at `http://localhost:8000` (or your configured port).

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `API_URL` | Target API endpoint to monitor | - | ✅ |
| `API_AUTHORIZATION` | Bearer token for API access | - | ✅ |
| `API_COOKIE` | Cookie header for API requests | - | ❌ |
| `MONITOR_INTERVAL_MINUTES` | How often to check for changes | `5` | ❌ |
| `PROXY_PORT` | Port for proxy server | `8000` | ❌ |
| `TARGET_HOST` | Target host for proxy | - | ✅ |
| `WECOM_WEBHOOK_URL` | WeChat Work webhook URL | - | ❌ |
| `WECOM_MENTIONED_LIST` | Users to mention in notifications | `@all` | ❌ |
| `LOG_FILE` | Log file path | `logs/requests.log` | ❌ |

### Deno Tasks

```bash
# Development
deno task dev:monitor          # Monitor with auto-reload
deno task dev:proxy           # Proxy with auto-reload

# Production
deno task start:monitor       # Start monitor
deno task start:proxy        # Start proxy server

# Testing
deno task test               # Run all tests

# Docker
deno task docker:build       # Build Docker image
deno task docker:run         # Run container
```

## 🧪 Testing

```bash
# Run all tests
deno test --allow-net --allow-read --allow-write

# Test specific component
deno test tests/wecom-webhook.test.ts

# Test with coverage
deno test --coverage=coverage --allow-net --allow-read --allow-write
```

## 📊 Monitoring & Logs

### Log Files

- **Monitor logs**: `logs/monitor.log` - API monitoring activity
- **Proxy logs**: `logs/proxy.log` - HTTP proxy requests
- **Docker logs**: Use `docker-compose logs -f` to view container logs

### Log Viewer (Optional)

Enable the web-based log viewer:

```bash
docker-compose --profile logs up -d
```

Access at `http://localhost:9999` to view real-time container logs.

## 🚀 Deployment

### Docker Production Deployment

1. **Prepare Environment**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

2. **Deploy Services**
   ```bash
   docker-compose up -d
   ```

3. **Monitor Health**
   ```bash
   docker-compose ps
   docker-compose logs -f
   ```

### Manual Deployment

1. **Install Dependencies**
   ```bash
   # Deno caches dependencies automatically
   deno cache src/monitor.ts src/proxy-server.ts
   ```

2. **Start Services**
   ```bash
   # Use a process manager like PM2 or systemd
   deno run --allow-net --allow-env --allow-read --allow-write src/monitor.ts &
   deno run --allow-net --allow-env --allow-read --allow-write src/proxy-server.ts &
   ```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Run tests**: `deno task test`
5. **Commit changes**: `git commit -m 'Add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Development Setup

```bash
# Clone your fork
git clone https://github.com/yourusername/api-monitor-proxy.git
cd api-monitor-proxy

# Set up environment
cp .env.example .env
# Edit .env with test values

# Run in development mode
deno task dev:monitor
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Open an issue on GitHub
- **WeChat Work Setup**: See [docs/WECOM_SETUP.md](docs/WECOM_SETUP.md)

## 🔗 Related Projects

- **[Deno](https://deno.land/)** - Modern JavaScript/TypeScript runtime
- **[WeChat Work API](https://work.weixin.qq.com/)** - Enterprise communication platform
- **[Docker](https://www.docker.com/)** - Containerization platform
