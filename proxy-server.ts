#!/usr/bin/env -S deno run --allow-net --allow-read

const TARGET_HOST = "https://shop-api.aixingheshangcheng.com";
const PORT = 8000;

interface ProxyRequestInfo {
  method: string;
  path: string;
  headers: Headers;
  body?: string;
}

async function logRequest(info: ProxyRequestInfo): Promise<void> {
  console.log(`\n=== Request Details ===`);
  console.log(`Method: ${info.method}`);
  console.log(`Path: ${info.path}`);
  console.log(`Headers:`, Object.fromEntries(info.headers.entries()));
  
  if (info.method === "POST" && info.body) {
    console.log(`Body:`, info.body);
  }
  console.log(`========================\n`);
}

async function forwardRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const targetUrl = `${TARGET_HOST}${url.pathname}${url.search}`;
  
  // Clone headers and modify the Host header
  const headers = new Headers(request.headers);
  headers.set("Host", "shop-api.aixingheshangcheng.com");
  
  // Get request body if it exists
  let body: string | null = null;
  if (request.method !== "GET" && request.method !== "HEAD") {
    try {
      body = await request.text();
    } catch {
      body = null;
    }
  }
  
  // Log the request details
  await logRequest({
    method: request.method,
    path: `${url.pathname}${url.search}`,
    headers: request.headers,
    body: body || undefined
  });
  
  try {
    // Forward the request
    const forwardedRequest = new Request(targetUrl, {
      method: request.method,
      headers: headers,
      body: body,
    });
    
    console.log(`Forwarding to: ${targetUrl}`);
    const response = await fetch(forwardedRequest);
    
    // Clone the response to return it
    const responseBody = await response.text();
    
    // Log response details
    console.log(`\n=== Response Details ===`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    console.log(`Response Headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`Response Body:`, responseBody.length > 1000 ? 
      responseBody.substring(0, 1000) + '... (truncated)' : responseBody);
    console.log(`=========================\n`);
    
    return new Response(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
  } catch (error) {
    console.error(`Error forwarding request:`, error);
    return new Response(
      JSON.stringify({ error: "Failed to forward request", details: (error as Error).message }), 
      { 
        status: 500, 
        headers: { "Content-Type": "application/json" } 
      }
    );
  }
}

async function handleRequest(request: Request): Promise<Response> {
  // Add CORS headers for browser compatibility
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  };
  
  // Handle preflight OPTIONS requests
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }
  
  try {
    const response = await forwardRequest(request);
    
    // Add CORS headers to the response
    const responseHeaders = new Headers(response.headers);
    Object.entries(corsHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error("Error handling request:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error" }), 
      { 
        status: 500, 
        headers: { 
          "Content-Type": "application/json",
          ...corsHeaders
        } 
      }
    );
  }
}

async function startServer(): Promise<void> {
  console.log(`🚀 Proxy server starting on http://localhost:${PORT}`);
  console.log(`📡 Forwarding requests to: ${TARGET_HOST}`);
  console.log(`🔄 Press Ctrl+C to stop the server\n`);
  
  // @ts-ignore: Deno types
  const server = Deno.serve({ port: PORT }, handleRequest);
  
  // Handle graceful shutdown
  const cleanup = () => {
    console.log("\n👋 Shutting down proxy server...");
    server.shutdown();
    // @ts-ignore: Deno types
    Deno.exit(0);
  };
  
  // Listen for interrupt signals
  // @ts-ignore: Deno types
  Deno.addSignalListener("SIGINT", cleanup);
  // @ts-ignore: Deno types
  Deno.addSignalListener("SIGTERM", cleanup);
  
  await server.finished;
}

// @ts-ignore: Deno main check
if (import.meta.main) {
  startServer().catch((error) => {
    console.error("Failed to start server:", error);
    // @ts-ignore: Deno types
    Deno.exit(1);
  });
}
