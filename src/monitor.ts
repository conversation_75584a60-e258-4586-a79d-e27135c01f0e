#!/usr/bin/env -S deno run --allow-net --allow-env --allow-read --allow-write

/**
 * API Monitor with Diff Detection and WeChat Work Notifications
 * 
 * This script monitors an API endpoint for changes and sends notifications
 * to WeChat Work (企业微信) when differences are detected.
 */

import { loadConfig, validateConfig, type Config } from "./config.ts";
import { sendDiffNotification, type DiffInfo } from "./wecom-webhook.ts";

let lastResponseBody: string | null = null;
let config: Config;

/**
 * Initialize the monitor with configuration
 */
function initializeMonitor(): void {
  try {
    config = loadConfig();
    validateConfig(config);
    console.log("🚀 API Monitor initialized");
    console.log(`📡 Monitoring: ${config.apiUrl}`);
    console.log(`⏱️  Interval: ${config.monitorIntervalMinutes} minutes`);
    if (config.wecomWebhookUrl) {
      console.log("📱 WeChat Work notifications: Enabled");
    } else {
      console.log("📱 WeChat Work notifications: Disabled (no webhook URL configured)");
    }
    console.log("");
  } catch (error) {
    console.error("❌ Failed to initialize monitor:", error.message);
    console.log("\n💡 Please check your .env file configuration.");
    console.log("   Copy .env.example to .env and update the values.");
    // @ts-ignore: Deno exit
    Deno.exit(1);
  }
}

/**
 * Log request details to file
 */
async function logToFile(message: string): Promise<void> {
  try {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    // Ensure logs directory exists
    try {
      // @ts-ignore: Deno file operations
      await Deno.mkdir("logs", { recursive: true });
    } catch {
      // Directory might already exist
    }
    
    // @ts-ignore: Deno file operations
    await Deno.writeTextFile(config.logFile, logEntry, { append: true });
  } catch (error) {
    console.error("Failed to write to log file:", error);
  }
}

/**
 * Make API request and check for changes
 */
async function makeRequest(): Promise<void> {
  try {
    const headers = {
      "accept": "application/json",
      "accept-encoding": "gzip",
      "authorization": config.apiAuthorization,
      "connection": "keep-alive",
      "content-type": "application/json",
      "cookie": config.apiCookie,
      "user-agent": config.userAgent,
      "platform": "android",
      "version": "3.9.3",
    };

    // Extract request body from config or use default
    const body = JSON.stringify({
      "id": "0",
      "type": "1",
    });

    const response = await fetch(config.apiUrl, {
      method: "POST",
      headers: headers,
      body: body,
    });

    if (!response.ok) {
      const errorMsg = `Request failed with status: ${response.status}`;
      console.error(errorMsg);
      await logToFile(`ERROR: ${errorMsg}`);
      return;
    }

    const responseBody = await response.text();
    const timestamp = new Date().toISOString();

    if (lastResponseBody === null) {
      console.log("✅ Initial request successful. Monitoring for changes...");
      await logToFile("Monitor started - Initial request successful");
      lastResponseBody = responseBody;
    } else if (lastResponseBody !== responseBody) {
      console.log("🔔 Response body has changed!");
      console.log(`📊 Previous length: ${lastResponseBody.length} characters`);
      console.log(`📊 New length: ${responseBody.length} characters`);
      
      await logToFile(`CHANGE DETECTED: Content changed from ${lastResponseBody.length} to ${responseBody.length} characters`);
      
      // Send WeChat Work notification if configured
      if (config.wecomWebhookUrl) {
        const diffInfo: DiffInfo = {
          timestamp,
          previousContent: lastResponseBody,
          newContent: responseBody,
          apiUrl: config.apiUrl,
        };
        
        await sendDiffNotification(
          config.wecomWebhookUrl,
          diffInfo,
          config.wecomMentionedList,
          config.wecomMentionedMobileList
        );
      }
      
      lastResponseBody = responseBody;
    } else {
      console.log("✅ No changes detected.");
      await logToFile("No changes detected");
    }
  } catch (error) {
    const errorMsg = `An error occurred: ${error.message}`;
    console.error(errorMsg);
    await logToFile(`ERROR: ${errorMsg}`);
  }
}

/**
 * Start the monitoring process
 */
async function startMonitoring(): Promise<void> {
  initializeMonitor();

  // Initial request
  await makeRequest();

  // Set up interval
  const intervalMs = config.monitorIntervalMinutes * 60 * 1000;
  setInterval(makeRequest, intervalMs);

  console.log("🔄 Monitoring started. Press Ctrl+C to stop.");
}

// Handle graceful shutdown
const cleanup = () => {
  console.log("\n👋 Shutting down monitor...");
  // @ts-ignore: Deno exit
  Deno.exit(0);
};

// Listen for interrupt signals
// @ts-ignore: Deno signal handling
Deno.addSignalListener("SIGINT", cleanup);
// @ts-ignore: Deno signal handling
Deno.addSignalListener("SIGTERM", cleanup);

// Start monitoring when script is executed directly
if (import.meta.main) {
  startMonitoring().catch((error) => {
    console.error("Failed to start monitoring:", error);
    // @ts-ignore: Deno exit
    Deno.exit(1);
  });
}
