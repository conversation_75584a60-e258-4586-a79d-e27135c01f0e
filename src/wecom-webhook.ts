/**
 * WeChat Work (企业微信) webhook integration
 */

export interface WeComMessage {
  msgtype: "text" | "markdown";
  text?: {
    content: string;
    mentioned_list?: string[];
    mentioned_mobile_list?: string[];
  };
  markdown?: {
    content: string;
    mentioned_list?: string[];
    mentioned_mobile_list?: string[];
  };
}

export interface DiffInfo {
  timestamp: string;
  previousContent: string;
  newContent: string;
  apiUrl: string;
}

/**
 * Send a message to WeChat Work webhook
 */
export async function sendWeComMessage(
  webhookUrl: string,
  message: WeComMessage
): Promise<boolean> {
  try {
    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      console.error(`WeChat Work webhook failed: ${response.status} ${response.statusText}`);
      return false;
    }

    const result = await response.json();
    if (result.errcode !== 0) {
      console.error(`WeChat Work webhook error: ${result.errmsg}`);
      return false;
    }

    console.log("✅ WeChat Work notification sent successfully");
    return true;
  } catch (error) {
    console.error("Failed to send WeChat Work notification:", error);
    return false;
  }
}

/**
 * Create a diff notification message for WeChat Work
 */
export function createDiffMessage(
  diffInfo: DiffInfo,
  mentionedList?: string,
  mentionedMobileList?: string
): WeComMessage {
  const timestamp = new Date(diffInfo.timestamp).toLocaleString("zh-CN", {
    timeZone: "Asia/Shanghai",
  });

  // Create a summary of changes
  const prevLength = diffInfo.previousContent.length;
  const newLength = diffInfo.newContent.length;
  const lengthDiff = newLength - prevLength;
  const lengthChange = lengthDiff > 0 ? `+${lengthDiff}` : `${lengthDiff}`;

  const content = `🔔 **API 响应变化检测**

**时间**: ${timestamp}
**API**: ${diffInfo.apiUrl}
**变化**: 内容长度 ${prevLength} → ${newLength} (${lengthChange} 字符)

**详细信息**:
- 监控系统检测到 API 响应内容发生变化
- 建议检查相关业务逻辑是否受到影响

> 如需查看详细差异，请检查监控日志`;

  const mentioned_list = mentionedList ? mentionedList.split(",").map(s => s.trim()) : undefined;
  const mentioned_mobile_list = mentionedMobileList ? mentionedMobileList.split(",").map(s => s.trim()) : undefined;

  return {
    msgtype: "markdown",
    markdown: {
      content,
      mentioned_list,
      mentioned_mobile_list,
    },
  };
}

/**
 * Send a diff notification to WeChat Work
 */
export async function sendDiffNotification(
  webhookUrl: string,
  diffInfo: DiffInfo,
  mentionedList?: string,
  mentionedMobileList?: string
): Promise<boolean> {
  const message = createDiffMessage(diffInfo, mentionedList, mentionedMobileList);
  return await sendWeComMessage(webhookUrl, message);
}
