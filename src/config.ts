/**
 * Configuration management for the API Monitor Proxy
 */

export interface Config {
  // API Configuration
  apiUrl: string;
  apiAuthorization: string;
  apiCookie: string;
  userAgent: string;
  
  // Monitoring Configuration
  monitorIntervalMinutes: number;
  logFile: string;
  
  // Proxy Configuration
  proxyPort: number;
  targetHost: string;
  
  // WeChat Work Configuration
  wecomWebhookUrl?: string;
  wecomMentionedList?: string;
  wecomMentionedMobileList?: string;
}

/**
 * Load configuration from environment variables
 */
export function loadConfig(): Config {
  // Helper function to get environment variable
  const getEnv = (key: string, defaultValue?: string): string => {
    // @ts-ignore: Deno env
    const value = Deno.env.get(key);
    if (value !== undefined) return value;
    if (defaultValue !== undefined) return defaultValue;
    throw new Error(`Environment variable ${key} is required`);
  };

  return {
    // API Configuration
    apiUrl: getEnv("API_URL", "https://example.com/api/v1/endpoint"),
    apiAuthorization: getEnv("API_AUTHORIZATION", "Bearer your-token-here"),
    apiCookie: getEnv("API_COOKIE", ""),
    userAgent: getEnv("USER_AGENT", "Mozilla/5.0 (compatible; API-Monitor/1.0)"),
    
    // Monitoring Configuration
    monitorIntervalMinutes: parseInt(getEnv("MONITOR_INTERVAL_MINUTES", "5")),
    logFile: getEnv("LOG_FILE", "logs/requests.log"),
    
    // Proxy Configuration
    proxyPort: parseInt(getEnv("PROXY_PORT", "8000")),
    targetHost: getEnv("TARGET_HOST", "https://example.com"),
    
    // WeChat Work Configuration
    wecomWebhookUrl: getEnv("WECOM_WEBHOOK_URL"),
    wecomMentionedList: getEnv("WECOM_MENTIONED_LIST", "@all"),
    wecomMentionedMobileList: getEnv("WECOM_MENTIONED_MOBILE_LIST", ""),
  };
}

/**
 * Validate configuration
 */
export function validateConfig(config: Config): void {
  if (!config.apiUrl || config.apiUrl === "https://example.com/api/v1/endpoint") {
    console.warn("⚠️  API_URL is not configured properly. Please set it in your .env file.");
  }
  
  if (!config.apiAuthorization || config.apiAuthorization === "Bearer your-token-here") {
    console.warn("⚠️  API_AUTHORIZATION is not configured properly. Please set it in your .env file.");
  }
  
  if (config.monitorIntervalMinutes < 1) {
    throw new Error("MONITOR_INTERVAL_MINUTES must be at least 1");
  }
  
  if (config.proxyPort < 1 || config.proxyPort > 65535) {
    throw new Error("PROXY_PORT must be between 1 and 65535");
  }
}
