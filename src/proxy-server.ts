#!/usr/bin/env -S deno run --allow-net --allow-read --allow-write --allow-env

/**
 * API Proxy Server with Request Logging
 * 
 * A proxy server that forwards HTTP requests to a target API while logging
 * request details and supporting CORS for browser compatibility.
 */

import { loadConfig, validateConfig, type Config } from "./config.ts";

let config: Config;

interface ProxyRequestInfo {
  method: string;
  path: string;
  headers: Headers;
  body?: string;
}

/**
 * Initialize the proxy server with configuration
 */
function initializeProxy(): void {
  try {
    config = loadConfig();
    validateConfig(config);
    console.log("🚀 Proxy Server initialized");
    console.log(`📡 Target: ${config.targetHost}`);
    console.log(`🔗 Port: ${config.proxyPort}`);
    console.log(`📝 Logging to: ${config.logFile}`);
    console.log("");
  } catch (error) {
    console.error("❌ Failed to initialize proxy:", error.message);
    console.log("\n💡 Please check your .env file configuration.");
    console.log("   Copy .env.example to .env and update the values.");
    // @ts-ignore: Deno exit
    Deno.exit(1);
  }
}

/**
 * Log request details to console and file
 */
async function logRequest(info: ProxyRequestInfo): Promise<void> {
  console.log(`\n=== Request Details ===`);
  console.log(`Method: ${info.method}`);
  console.log(`Path: ${info.path}`);
  console.log(`Headers:`, Object.fromEntries(info.headers.entries()));
  
  if (info.method === "POST" && info.body) {
    console.log(`Body:`, info.body);
  }
  console.log(`========================\n`);

  // Log to file
  try {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${info.method} ${info.path}\n`;
    
    // Ensure logs directory exists
    try {
      // @ts-ignore: Deno file operations
      await Deno.mkdir("logs", { recursive: true });
    } catch {
      // Directory might already exist
    }
    
    // @ts-ignore: Deno file operations
    await Deno.writeTextFile(config.logFile, logEntry, { append: true });
  } catch (error) {
    console.error("Failed to write to log file:", error);
  }
}

/**
 * Generate curl command equivalent for the request
 */
function generateCurlCommand(
  method: string,
  url: string,
  headers: Headers,
  body?: string | null
): string {
  let curl = `curl -X ${method}`;
  
  // Add headers
  for (const [key, value] of headers.entries()) {
    // Skip some headers that curl adds automatically or that might cause issues
    if (!["host", "content-length", "connection"].includes(key.toLowerCase())) {
      curl += ` -H "${key}: ${value}"`;
    }
  }
  
  // Add body if present
  if (body && method !== "GET" && method !== "HEAD") {
    // Escape quotes in the body
    const escapedBody = body.replace(/"/g, '\\"');
    curl += ` -d "${escapedBody}"`;
  }
  
  // Add URL at the end
  curl += ` "${url}"`;
  
  return curl;
}

/**
 * Decode Unicode escape sequences in JSON strings
 */
function decodeUnicodeInJson(jsonString: string): string {
  try {
    // First, try to parse as JSON to validate it
    const parsed = JSON.parse(jsonString);
    
    // Convert back to JSON with proper Unicode decoding
    return JSON.stringify(parsed, null, 2);
  } catch (error) {
    // If it's not valid JSON, just decode Unicode escape sequences manually
    return jsonString.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
      return String.fromCharCode(parseInt(hex, 16));
    });
  }
}

/**
 * Handle incoming HTTP requests
 */
async function handleRequest(request: Request): Promise<Response> {
  // Add CORS headers for browser compatibility
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  };
  
  // Handle preflight OPTIONS requests
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  try {
    const url = new URL(request.url);
    const targetUrl = `${config.targetHost}${url.pathname}${url.search}`;
    
    // Clone headers and update Host header
    const headers = new Headers(request.headers);
    const targetHostUrl = new URL(config.targetHost);
    headers.set("Host", targetHostUrl.host);
    
    // Get request body if it exists
    let body: string | null = null;
    if (request.method !== "GET" && request.method !== "HEAD") {
      try {
        body = await request.text();
      } catch {
        body = null;
      }
    }
    
    // Log the request details
    await logRequest({
      method: request.method,
      path: `${url.pathname}${url.search}`,
      headers: request.headers,
      body: body || undefined
    });

    // Generate and log curl command
    const curlCommand = generateCurlCommand(request.method, targetUrl, headers, body);
    console.log("📋 Equivalent curl command:");
    console.log(curlCommand);
    console.log("");

    // Forward the request
    const response = await fetch(targetUrl, {
      method: request.method,
      headers: headers,
      body: body,
    });

    // Get response body
    const responseBody = await response.text();
    
    // Log response details
    console.log(`=== Response Details ===`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    console.log(`Headers:`, Object.fromEntries(response.headers.entries()));
    
    // Decode Unicode in response body for console display
    let displayResponseBody = responseBody;
    const responseContentType = response.headers.get('content-type') || '';
    if (responseContentType.includes('application/json')) {
      try {
        displayResponseBody = decodeUnicodeInJson(responseBody);
      } catch (error) {
        console.warn("Failed to decode Unicode in response body for display:", error);
      }
    }
    console.log(`Response Body:`, displayResponseBody);
    console.log(`=========================\n`);

    // Create response with CORS headers
    const responseHeaders = new Headers(response.headers);
    Object.entries(corsHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });

    return new Response(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error("Proxy error:", error);
    
    return new Response(
      JSON.stringify({ 
        error: "Proxy server error", 
        message: error.message 
      }), 
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      }
    );
  }
}

/**
 * Start the proxy server
 */
async function startServer(): Promise<void> {
  initializeProxy();
  
  console.log(`🚀 Proxy server starting on http://localhost:${config.proxyPort}`);
  console.log(`📡 Forwarding requests to: ${config.targetHost}`);
  console.log(`🔄 Press Ctrl+C to stop the server\n`);
  
  // @ts-ignore: Deno types
  const server = Deno.serve({ port: config.proxyPort }, handleRequest);
  
  // Handle graceful shutdown
  const cleanup = () => {
    console.log("\n👋 Shutting down proxy server...");
    server.shutdown();
    // @ts-ignore: Deno types
    Deno.exit(0);
  };
  
  // Listen for interrupt signals
  // @ts-ignore: Deno types
  Deno.addSignalListener("SIGINT", cleanup);
  // @ts-ignore: Deno types
  Deno.addSignalListener("SIGTERM", cleanup);
  
  await server.finished;
}

// Start server when script is executed directly
if (import.meta.main) {
  startServer().catch((error) => {
    console.error("Failed to start server:", error);
    // @ts-ignore: Deno types
    Deno.exit(1);
  });
}
