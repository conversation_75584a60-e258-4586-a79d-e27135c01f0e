#!/usr/bin/env -S deno run --allow-net

// Test script for the proxy server
const PROXY_URL = "http://localhost:8000";

async function testGetRequest() {
  console.log("🧪 Testing GET request...");
  try {
    const response = await fetch(`${PROXY_URL}/api/test`);
    console.log(`Status: ${response.status}`);
    console.log(`Headers:`, Object.fromEntries(response.headers.entries()));
    const text = await response.text();
    console.log(`Response: ${text.slice(0, 200)}...`);
  } catch (error) {
    console.error("GET request failed:", error.message);
  }
  console.log("");
}

async function testPostRequest() {
  console.log("🧪 Testing POST request...");
  try {
    const testData = {
      message: "Hello from test script",
      timestamp: new Date().toISOString(),
      data: { key: "value", number: 42 }
    };

    const response = await fetch(`${PROXY_URL}/api/test`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Test-Header": "test-value"
      },
      body: JSON.stringify(testData)
    });

    console.log(`Status: ${response.status}`);
    console.log(`Headers:`, Object.fromEntries(response.headers.entries()));
    const text = await response.text();
    console.log(`Response: ${text.slice(0, 200)}...`);
  } catch (error) {
    console.error("POST request failed:", error.message);
  }
  console.log("");
}

async function runTests() {
  console.log("🚀 Starting proxy server tests...\n");
  console.log("⚠️  Make sure the proxy server is running on port 8000");
  console.log("   Run: deno run --allow-net proxy-server.ts\n");
  
  await testGetRequest();
  await testPostRequest();
  
  console.log("✅ Tests completed!");
}

// Run tests when script is executed directly
runTests().catch(console.error);
