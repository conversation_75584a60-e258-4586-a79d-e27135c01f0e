{"name": "api-monitor-proxy", "version": "1.0.0", "description": "A Deno-based API monitoring and proxy system with diff detection and webhook notifications", "author": "Your Name", "license": "MIT", "exports": {"./monitor": "./src/monitor.ts", "./proxy": "./src/proxy-server.ts"}, "tasks": {"start:proxy": "deno run --allow-net --allow-read --allow-write src/proxy-server.ts", "start:monitor": "deno run --allow-net --allow-env src/monitor.ts", "test": "deno test --allow-net --allow-read --allow-write tests/", "dev:proxy": "deno run --allow-net --allow-read --allow-write --watch src/proxy-server.ts", "dev:monitor": "deno run --allow-net --allow-env --watch src/monitor.ts", "docker:build": "docker build -t api-monitor-proxy .", "docker:run": "docker run -p 8000:8000 api-monitor-proxy"}, "imports": {"@std/": "https://deno.land/std@0.208.0/"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["src/", "tests/"], "exclude": ["node_modules/", "dist/"]}, "lint": {"include": ["src/", "tests/"], "exclude": ["node_modules/", "dist/"], "rules": {"tags": ["recommended"]}}}