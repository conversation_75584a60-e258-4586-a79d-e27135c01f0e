# Use the official Deno image
FROM denoland/deno:1.40.2

# Set the working directory
WORKDIR /app

# Copy the source code
COPY src/ ./src/
COPY deno.json ./
COPY .env.example ./

# Create logs directory
RUN mkdir -p logs

# Cache dependencies
RUN deno cache src/monitor.ts src/proxy-server.ts

# Create a non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose the proxy server port
EXPOSE 8000

# Default command (can be overridden)
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--allow-write", "src/monitor.ts"]
