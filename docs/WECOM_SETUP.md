# WeChat Work (企业微信) Webhook Setup Guide

This guide explains how to set up WeChat Work webhook notifications for the API Monitor Proxy.

## Prerequisites

1. A WeChat Work (企业微信) account with admin privileges
2. Access to create custom applications or bots

## Step 1: Create a WeChat Work Bot

1. **Login to WeChat Work Admin Console**
   - Go to [https://work.weixin.qq.com/](https://work.weixin.qq.com/)
   - Login with your admin account

2. **Create a Group Bot**
   - Create or join a group chat where you want to receive notifications
   - In the group chat, click on the group settings (⚙️)
   - Select "Group Bots" (群机器人)
   - Click "Add Bot" (添加机器人)
   - Choose "Custom Bot" (自定义机器人)

3. **Configure the Bot**
   - Give your bot a name (e.g., "API Monitor")
   - Optionally upload an avatar
   - Click "Create" (创建)

4. **Get the Webhook URL**
   - After creation, you'll receive a webhook URL
   - It looks like: `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY_HERE`
   - **Important**: Save this URL securely - it's your bot's access token

## Step 2: Configure the Application

1. **Set Environment Variables**
   
   Add the following to your `.env` file:
   ```bash
   # WeChat Work Webhook Configuration
   WECOM_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY_HERE
   WECOM_MENTIONED_LIST=@all
   WECOM_MENTIONED_MOBILE_LIST=
   ```

2. **Configuration Options**
   
   - `WECOM_WEBHOOK_URL`: The webhook URL from Step 1
   - `WECOM_MENTIONED_LIST`: Comma-separated list of users to mention
     - Use `@all` to mention everyone
     - Use specific usernames: `user1,user2,user3`
   - `WECOM_MENTIONED_MOBILE_LIST`: Comma-separated list of mobile numbers to mention
     - Example: `13800138000,13900139000`

## Step 3: Test the Integration

1. **Test with the Monitor**
   ```bash
   # Start the monitor
   deno run --allow-net --allow-env --allow-read --allow-write src/monitor.ts
   ```

2. **Test with Docker**
   ```bash
   # Make sure your .env is configured
   docker-compose up monitor
   ```

3. **Manual Test**
   
   You can test the webhook directly:
   ```bash
   curl -X POST "YOUR_WEBHOOK_URL" \
     -H "Content-Type: application/json" \
     -d '{
       "msgtype": "text",
       "text": {
         "content": "🧪 Test message from API Monitor Proxy",
         "mentioned_list": ["@all"]
       }
     }'
   ```

## Message Format

The API Monitor sends notifications in this format:

```
🔔 **API 响应变化检测**

**时间**: 2025-01-01 20:00:00
**API**: https://your-api.com/endpoint
**变化**: 内容长度 1234 → 1456 (+222 字符)

**详细信息**:
- 监控系统检测到 API 响应内容发生变化
- 建议检查相关业务逻辑是否受到影响

> 如需查看详细差异，请检查监控日志
```

## Troubleshooting

### Common Issues

1. **"Invalid webhook URL" error**
   - Verify the webhook URL is correct
   - Make sure the key parameter is included
   - Check that the bot hasn't been deleted

2. **Messages not being sent**
   - Verify the bot is still in the group
   - Check network connectivity
   - Ensure the webhook URL hasn't expired

3. **Mentions not working**
   - Verify usernames are correct (case-sensitive)
   - For mobile mentions, use the exact mobile number format
   - `@all` should work in most cases

### Testing Webhook

Use the test script to verify your webhook:

```bash
# Run the webhook test
deno test --allow-net tests/wecom-webhook.test.ts
```

### Rate Limits

WeChat Work has rate limits for webhook messages:
- Maximum 20 messages per minute per bot
- The monitor respects these limits by only sending notifications when changes are detected

## Security Notes

1. **Keep webhook URLs secret** - they provide direct access to send messages
2. **Use environment variables** - never commit webhook URLs to version control
3. **Monitor usage** - regularly check that only expected messages are being sent
4. **Rotate keys** - consider regenerating webhook URLs periodically

## Advanced Configuration

### Custom Message Templates

You can modify the message format by editing `src/wecom-webhook.ts`:

```typescript
// Customize the createDiffMessage function
export function createDiffMessage(diffInfo: DiffInfo, ...): WeComMessage {
  const content = `Your custom message template here...`;
  // ... rest of the function
}
```

### Multiple Webhooks

To send to multiple groups, you can set up multiple webhook URLs:

```bash
WECOM_WEBHOOK_URL_1=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=KEY1
WECOM_WEBHOOK_URL_2=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=KEY2
```

Then modify the monitor to send to multiple endpoints.
