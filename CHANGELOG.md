# Changelog

All notable changes to this project will be documented in this file.

## [1.0.0] - 2025-01-01

### Added
- **Open Source Release** - Complete project restructure for open source distribution
- **WeChat Work Integration** - Full webhook notification support for diff detection
- **Docker Support** - Complete containerization with docker-compose setup
- **Environment Configuration** - Comprehensive .env-based configuration system
- **Enhanced Monitoring** - Improved API monitoring with detailed logging
- **Proxy Server** - Enhanced HTTP proxy with request logging and CORS support
- **Test Suite** - Comprehensive test coverage for core functionality
- **Documentation** - Complete setup guides and API documentation

### Features

#### API Monitoring
- Real-time API endpoint monitoring with configurable intervals
- Automatic diff detection for API response changes
- WeChat Work webhook notifications when changes are detected
- Comprehensive logging with timestamps and detailed information
- Graceful error handling and recovery

#### Proxy Server
- HTTP proxy supporting all methods (GET, POST, PUT, DELETE, etc.)
- Request and response logging with Unicode decoding
- CORS support for browser compatibility
- Automatic curl command generation for debugging
- Configurable target host and port

#### DevOps & Deployment
- Docker containerization with multi-service setup
- Docker Compose orchestration for easy deployment
- Environment-based configuration management
- Optional log viewer service for real-time monitoring
- Graceful shutdown handling for all services

#### WeChat Work Integration
- Markdown-formatted notifications with Chinese localization
- Configurable mention lists (@all, specific users, mobile numbers)
- Automatic message formatting with change summaries
- Rate limiting compliance with WeChat Work API
- Comprehensive error handling and retry logic

### Technical Improvements
- **TypeScript** - Full type safety with Deno's native TypeScript support
- **Modular Architecture** - Clean separation of concerns with dedicated modules
- **Configuration Management** - Centralized config with validation
- **Error Handling** - Comprehensive error handling throughout the application
- **Logging** - Structured logging with file and console output
- **Testing** - Unit tests with Deno's built-in test runner

### Documentation
- Complete README with setup instructions
- WeChat Work webhook setup guide
- Docker deployment documentation
- API reference and configuration guide
- Contributing guidelines and development setup

### Migration from Legacy Version
- Moved from hardcoded configuration to environment variables
- Restructured codebase into `src/` directory
- Added proper TypeScript types and interfaces
- Enhanced error handling and logging
- Added comprehensive test coverage

### Breaking Changes
- Configuration now requires `.env` file setup
- File structure changed - main files moved to `src/` directory
- Command line usage updated to use `deno task` commands
- Docker deployment replaces manual process management

### Security Improvements
- Removed hardcoded API credentials from source code
- Added `.gitignore` to prevent accidental credential commits
- Environment variable validation and warnings
- Secure Docker container setup with non-root user

### Performance Improvements
- Optimized Docker image with dependency caching
- Efficient diff detection algorithm
- Reduced memory footprint with streaming responses
- Configurable monitoring intervals to reduce API load
