#!/usr/bin/env -S deno run --allow-net --allow-read --allow-write

const TARGET_HOST = "https://shop-api.aixingheshangcheng.com";
const PORT = 8000;
const LOG_FILE = "curl-commands.log";

interface ProxyRequestInfo {
  method: string;
  path: string;
  headers: Headers;
  body?: string;
}

async function logRequest(info: ProxyRequestInfo): Promise<void> {
  console.log(`\n=== Request Details ===`);
  console.log(`Method: ${info.method}`);
  console.log(`Path: ${info.path}`);
  console.log(`Headers:`, Object.fromEntries(info.headers.entries()));
  
  if (info.method === "POST" && info.body) {
    // Decode Unicode in the body if it's JSON for better readability
    let displayBody = info.body;
    const contentType = info.headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      try {
        displayBody = decodeUnicodeInJson(info.body);
      } catch (error) {
        console.warn("Failed to decode Unicode in request body for display:", error);
      }
    }
    console.log(`Body:`, displayBody);
  }
  console.log(`========================\n`);
}

function generateCurlCommand(url: string, method: string, headers: Headers, body: string | null): string {
  let curl = `curl --compressed -X ${method}`;
  
  // Add headers
  for (const [key, value] of headers.entries()) {
    curl += ` -H "${key}: ${value}"`;
  }
  
  // Add body if present
  if (body && method !== "GET" && method !== "HEAD") {
    // Decode Unicode in the body if it's JSON
    let processedBody = body;
    const contentType = headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      try {
        processedBody = decodeUnicodeInJson(body);
      } catch (error) {
        console.warn("Failed to decode Unicode in request body:", error);
        // Keep original body if decoding fails
        processedBody = body;
      }
    }
    
    // Escape quotes in the body
    const escapedBody = processedBody.replace(/"/g, '\\"');
    curl += ` -d "${escapedBody}"`;
  }
  
  // Add URL at the end
  curl += ` "${url}"`;
  
  return curl;
}

async function writeCurlToLog(curlCommand: string): Promise<void> {
  try {
    const timestamp = new Date().toISOString();
    const logEntry = `# ${timestamp}\n${curlCommand}\n\n`;
    
    // @ts-ignore: Deno file operations
    await Deno.writeTextFile(LOG_FILE, logEntry, { append: true });
  } catch (error) {
    console.error("Failed to write to log file:", error);
  }
}

function decodeUnicodeInJson(jsonString: string): string {
  try {
    // First, try to parse as JSON to validate it
    const parsed = JSON.parse(jsonString);
    
    // Convert back to JSON with proper Unicode decoding
    // JSON.stringify will properly handle Unicode characters
    return JSON.stringify(parsed, null, 2);
  } catch (error) {
    // If it's not valid JSON, just decode Unicode escape sequences manually
    return jsonString.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
      return String.fromCharCode(parseInt(hex, 16));
    });
  }
}

async function writeForwardedRequestToLog(info: ProxyRequestInfo): Promise<void> {
  try {
    const timestamp = new Date().toISOString();
    let logEntry = `# FORWARDED REQUEST: ${timestamp}\n`;
    logEntry += `${info.method} ${info.path} HTTP/1.1\n`;
    for (const [key, value] of info.headers.entries()) {
      logEntry += `${key}: ${value}\n`;
    }
    if (info.body) {
      logEntry += `\n${info.body}\n`;
    }
    logEntry += `# END FORWARDED REQUEST\n\n`;
    
    // @ts-ignore: Deno file operations
    await Deno.writeTextFile(LOG_FILE, logEntry, { append: true });
  } catch (error) {
    console.error("Failed to write forwarded request to log file:", error);
  }
}

async function writeResponseToLog(status: number, statusText: string, headers: Headers, body: string): Promise<void> {
  try {
    let responseLog = `# RESPONSE\n`;
    responseLog += `HTTP/1.1 ${status} ${statusText}\n`;
    
    // Add response headers
    for (const [key, value] of headers.entries()) {
      responseLog += `${key}: ${value}\n`;
    }
    
    // Decode Unicode in the response body if it's JSON
    let decodedBody = body;
    const contentType = headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      try {
        decodedBody = decodeUnicodeInJson(body);
      } catch (error) {
        console.warn("Failed to decode Unicode in JSON response:", error);
        // Keep original body if decoding fails
        decodedBody = body;
      }
    }
    
    responseLog += `\n${decodedBody}\n\n`;
    responseLog += `# END RESPONSE\n\n`;
    
    // @ts-ignore: Deno file operations
    await Deno.writeTextFile(LOG_FILE, responseLog, { append: true });
  } catch (error) {
    console.error("Failed to write response to log file:", error);
  }
}

async function forwardRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const targetUrl = `${TARGET_HOST}${url.pathname}${url.search}`;
  
  // Clone headers and filter out Cloudflare headers
  const headers = new Headers();
  
  // List of Cloudflare headers to remove
  const cloudflareHeaders = [
    'cdn-loop',
    'cf-connecting-ip',
    'cf-ipcountry',
    'cf-ray',
    'cf-visitor',
    'cf-warp-tag-id',
    'x-forwarded-for',
    'x-forwarded-proto'
  ];
  
  // Copy headers excluding Cloudflare ones
  for (const [key, value] of request.headers.entries()) {
    if (!cloudflareHeaders.includes(key.toLowerCase())) {
      headers.set(key, value);
    }
  }
  
  // Set the Host header to target domain
  headers.set("Host", "shop-api.aixingheshangcheng.com");
  
  // Get request body if it exists
  let body: string | null = null;
  if (request.method !== "GET" && request.method !== "HEAD") {
    try {
      body = await request.text();
    } catch {
      body = null;
    }
  }
  
  // Log the request details
  await logRequest({
    method: request.method,
    path: `${url.pathname}${url.search}`,
    headers: request.headers,
    body: body || undefined
  });
  
  try {
    // Forward the request
    const forwardedRequest = new Request(targetUrl, {
      method: request.method,
      headers: headers,
      body: body,
    });
    
    // Log the forwarded request
    const forwardedUrl = new URL(forwardedRequest.url);
    await writeForwardedRequestToLog({
      method: forwardedRequest.method,
      path: `${forwardedUrl.pathname}${forwardedUrl.search}`,
      headers: forwardedRequest.headers,
      body: body || undefined,
    });

    console.log(`Forwarding to: ${targetUrl}`);
    
    // Generate equivalent curl command
    const curlCommand = generateCurlCommand(targetUrl, request.method, headers, body);
    console.log(`\n=== Equivalent cURL Command ===`);
    console.log(curlCommand);
    console.log(`===============================\n`);
    
    // Write curl command to log file
    await writeCurlToLog(curlCommand);
    
    const response = await fetch(forwardedRequest);
    
    // Clone the response to return it
    const responseBody = await response.text();
    
    // Log response details
    console.log(`\n=== Response Details ===`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    console.log(`Response Headers:`, Object.fromEntries(response.headers.entries()));
    
    // Decode Unicode in response body for console display
    let displayResponseBody = responseBody;
    const responseContentType = response.headers.get('content-type') || '';
    if (responseContentType.includes('application/json')) {
      try {
        displayResponseBody = decodeUnicodeInJson(responseBody);
      } catch (error) {
        console.warn("Failed to decode Unicode in response body for display:", error);
      }
    }
    console.log(`Response Body:`, displayResponseBody);
    console.log(`=========================\n`);
    
    // Write response to log file
    await writeResponseToLog(response.status, response.statusText, response.headers, responseBody);
    
    return new Response(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
  } catch (error) {
    console.error(`Error forwarding request:`, error);
    return new Response(
      JSON.stringify({ error: "Failed to forward request", details: (error as Error).message }), 
      { 
        status: 500, 
        headers: { "Content-Type": "application/json" } 
      }
    );
  }
}

async function handleRequest(request: Request): Promise<Response> {
  // Add CORS headers for browser compatibility
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  };
  
  // Handle preflight OPTIONS requests
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }
  
  try {
    const response = await forwardRequest(request);
    
    // Add CORS headers to the response
    const responseHeaders = new Headers(response.headers);
    Object.entries(corsHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error("Error handling request:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error" }), 
      { 
        status: 500, 
        headers: { 
          "Content-Type": "application/json",
          ...corsHeaders
        } 
      }
    );
  }
}

async function startServer(): Promise<void> {
  console.log(`🚀 Proxy server starting on http://localhost:${PORT}`);
  console.log(`📡 Forwarding requests to: ${TARGET_HOST}`);
  console.log(`� Logging curl commands to: ${LOG_FILE}`);
  console.log(`�🔄 Press Ctrl+C to stop the server\n`);
  
  // @ts-ignore: Deno types
  const server = Deno.serve({ port: PORT }, handleRequest);
  
  // Handle graceful shutdown
  const cleanup = () => {
    console.log("\n👋 Shutting down proxy server...");
    server.shutdown();
    // @ts-ignore: Deno types
    Deno.exit(0);
  };
  
  // Listen for interrupt signals
  // @ts-ignore: Deno types
  Deno.addSignalListener("SIGINT", cleanup);
  // @ts-ignore: Deno types
  Deno.addSignalListener("SIGTERM", cleanup);
  
  await server.finished;
}

// @ts-ignore: Deno main check
if (import.meta.main) {
  startServer().catch((error) => {
    console.error("Failed to start server:", error);
    // @ts-ignore: Deno types
    Deno.exit(1);
  });
}
