#!/bin/bash

# Docker Setup Script for API Monitor Proxy
# This script helps set up and run the containerized application

set -e

echo "🐳 API Monitor Proxy - Docker Setup"
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env file from .env.example"
        echo ""
        echo "🔧 Please edit .env file with your configuration:"
        echo "   - Set API_URL to your target API endpoint"
        echo "   - Set API_AUTHORIZATION with your bearer token"
        echo "   - Set WECOM_WEBHOOK_URL for WeChat Work notifications (optional)"
        echo ""
        echo "📝 Edit the file: nano .env"
        echo ""
        read -p "Press Enter after you've configured the .env file..."
    else
        echo "❌ .env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Create logs directory
mkdir -p logs

echo ""
echo "🚀 Building Docker images..."
docker-compose build

echo ""
echo "✅ Setup complete!"
echo ""
echo "Available commands:"
echo "  🔍 Start monitor only:     docker-compose up monitor"
echo "  🔗 Start proxy only:       docker-compose up proxy"
echo "  🚀 Start both services:    docker-compose up -d"
echo "  📊 View logs:              docker-compose logs -f"
echo "  🛑 Stop services:          docker-compose down"
echo "  📋 Start with log viewer:  docker-compose --profile logs up -d"
echo ""
echo "🌐 Services will be available at:"
echo "  - Proxy Server: http://localhost:8000"
echo "  - Log Viewer (if enabled): http://localhost:9999"
