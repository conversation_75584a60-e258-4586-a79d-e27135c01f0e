version: '3.8'

services:
  # API Monitor Service
  monitor:
    build: .
    container_name: api-monitor
    restart: unless-stopped
    environment:
      - API_URL=${API_URL}
      - API_AUTHORIZATION=${API_AUTHORIZATION}
      - API_COOKIE=${API_COOKIE}
      - MONITOR_INTERVAL_MINUTES=${MONITOR_INTERVAL_MINUTES:-5}
      - LOG_FILE=logs/monitor.log
      - WECOM_WEBHOOK_URL=${WECOM_WEBHOOK_URL}
      - WECOM_MENTIONED_LIST=${WECOM_MENTIONED_LIST:-@all}
      - WECOM_MENTIONED_MOBILE_LIST=${WECOM_MENTIONED_MOBILE_LIST}
      - USER_AGENT=${USER_AGENT}
    volumes:
      - ./logs:/app/logs
    command: ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--allow-write", "src/monitor.ts"]
    networks:
      - api-monitor-network

  # API Proxy Service
  proxy:
    build: .
    container_name: api-proxy
    restart: unless-stopped
    ports:
      - "${PROXY_PORT:-8000}:8000"
    environment:
      - TARGET_HOST=${TARGET_HOST}
      - PROXY_PORT=8000
      - LOG_FILE=logs/proxy.log
    volumes:
      - ./logs:/app/logs
    command: ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--allow-write", "src/proxy-server.ts"]
    networks:
      - api-monitor-network

  # Optional: Log viewer service (lightweight web interface)
  log-viewer:
    image: amir20/dozzle:latest
    container_name: log-viewer
    restart: unless-stopped
    ports:
      - "9999:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - DOZZLE_LEVEL=info
      - DOZZLE_TAILSIZE=300
    networks:
      - api-monitor-network
    profiles:
      - logs

networks:
  api-monitor-network:
    driver: bridge

volumes:
  logs:
    driver: local
