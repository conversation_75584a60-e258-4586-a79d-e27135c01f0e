#!/usr/bin/env -S deno test --allow-net

/**
 * Tests for WeChat Work webhook functionality
 */

import { assertEquals, assertExists } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { createDiffMessage, type DiffInfo } from "../src/wecom-webhook.ts";

Deno.test("createDiffMessage - should create proper WeChat Work message", () => {
  const diffInfo: DiffInfo = {
    timestamp: "2025-01-01T12:00:00.000Z",
    previousContent: "old content",
    newContent: "new content with more data",
    apiUrl: "https://api.example.com/test",
  };

  const message = createDiffMessage(diffInfo, "@all", "13800138000");

  assertEquals(message.msgtype, "markdown");
  assertExists(message.markdown);
  assertEquals(message.markdown.mentioned_list, ["@all"]);
  assertEquals(message.markdown.mentioned_mobile_list, ["13800138000"]);
  
  // Check that the message contains expected information
  const content = message.markdown.content;
  assertEquals(content.includes("API 响应变化检测"), true);
  assertEquals(content.includes("https://api.example.com/test"), true);

  // Check length change calculation (old content: 11 chars, new content: 27 chars)
  const oldLength = diffInfo.previousContent.length; // "old content" = 11
  const newLength = diffInfo.newContent.length; // "new content with more data" = 27
  assertEquals(content.includes(`${oldLength} → ${newLength}`), true);
});

Deno.test("createDiffMessage - should handle empty mention lists", () => {
  const diffInfo: DiffInfo = {
    timestamp: "2025-01-01T12:00:00.000Z",
    previousContent: "test",
    newContent: "test2",
    apiUrl: "https://api.example.com/test",
  };

  const message = createDiffMessage(diffInfo);

  assertEquals(message.msgtype, "markdown");
  assertExists(message.markdown);
  assertEquals(message.markdown.mentioned_list, undefined);
  assertEquals(message.markdown.mentioned_mobile_list, undefined);
});

Deno.test("createDiffMessage - should handle multiple mentions", () => {
  const diffInfo: DiffInfo = {
    timestamp: "2025-01-01T12:00:00.000Z",
    previousContent: "test",
    newContent: "test2",
    apiUrl: "https://api.example.com/test",
  };

  const message = createDiffMessage(diffInfo, "@all,user1,user2", "13800138000,13900139000");

  assertEquals(message.msgtype, "markdown");
  assertExists(message.markdown);
  assertEquals(message.markdown.mentioned_list, ["@all", "user1", "user2"]);
  assertEquals(message.markdown.mentioned_mobile_list, ["13800138000", "13900139000"]);
});

// Integration test (requires actual webhook URL)
Deno.test({
  name: "sendWeComMessage - integration test",
  ignore: true, // Set to false and provide webhook URL to test
  fn: async () => {
    // This test requires a real WeChat Work webhook URL
    // Uncomment and set WECOM_WEBHOOK_URL environment variable to test
    
    // const webhookUrl = Deno.env.get("WECOM_WEBHOOK_URL");
    // if (!webhookUrl) {
    //   console.log("Skipping integration test - no webhook URL provided");
    //   return;
    // }
    
    // const { sendWeComMessage } = await import("../src/wecom-webhook.ts");
    
    // const testMessage = {
    //   msgtype: "text" as const,
    //   text: {
    //     content: "🧪 Test message from API Monitor Proxy",
    //     mentioned_list: ["@all"],
    //   },
    // };
    
    // const result = await sendWeComMessage(webhookUrl, testMessage);
    // assertEquals(result, true);
  },
});
